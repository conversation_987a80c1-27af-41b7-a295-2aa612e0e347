
/* Hero Section with Background Image */
.hero {
  background: url('../images/banner-bg.png') no-repeat center;
  background-size: cover;
}
.nav-item a {
    font-weight: 600;
    font-size: 15px;
}
.nav-item a:hover {
    color: #13499f!important;
    text-decoration: none;
}
.head{
    position: relative;
    z-index: 1;
   
}
.fw-bold{
    font-weight: 700;
    color:white;
}
.mt-3{
    color: white;
}
.welcome-img{
    position: relative;
}
.welcome-con{
    color:black;
    font-weight: 600;
    text-align: justify;

}
.welcome-description{
    color: rgb(59, 55, 55);
    text-align: justify;
}
.welcome-section{
    padding: 50px 0px;
}
.small-1{
    position: absolute;
    top: 13px;
    right: 62px;
}
.small-2{
    position: absolute;
    top: 280px;
    right: 60px;

}

.features-section{
    padding: 50px 0px;
    background-color: #13499f;
}
.fh{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 250;
    font-size: 42px;
    color: white;
    text-align: center;
    margin-bottom: 30px;


}
.feature-card{
    padding: 30px;
    background-color: white;
    align-items: center;
    border-radius: 10px; 
    

    
}
.feature-card p{
    text-align: justify;
    font-size: 18;
    line-height: 1.6rem; 
}
.feature-card h3{
    text-align: justify;
    padding-top: 35px;
    font-size: 20px ;
    font-weight: 100px;
}

.notification-section{
    background-color: #bbebff;
    padding: 50px 0px;
}


.notify-text{
    margin-top: 140px;
}
.notify-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 40px;
font-weight: 40px;
}

.notify-text p{
    text-align: justify;
    font-size: 18px;  

}
.list{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }


.seamless-section{
    background-color: white;
    padding: 70px 0px;
}
.seamless-text{
    margin-top: 140px;
}
.seamless-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 40px;
font-weight: 40px;
}
.seamless-text p{
    text-align: justify;
    font-size: 18px;  

}
.list-1{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }

.flexibility-section{

    background-color: #13499f;
   

}
.flexibility-style{
    padding: 50px 0px; 
}