
/* Hero Section with Background Image */
.hero {
  background: url('../images/banner-bg.png') no-repeat center;
  background-size: cover;
}
.nav-item a {
    font-weight: 600;
    font-size: 15px;
}
.nav-item a:hover {
    color: #13499f!important;
    text-decoration: none;
}
.head{
    position: relative;
    z-index: 1;
   
}
.fw-bold{
    font-weight: 700;
    color:white;
}
.mt-3{
    color: white;
}
.welcome-img{
    position: relative;
}
.welcome-con{
    color:black;
    font-weight: 600;
    text-align: justify;

}
.welcome-description{
    color: rgb(59, 55, 55);
    text-align: justify;
}
.welcome-section{
    padding: 50px 0px;
}
.small-1{
    position: absolute;
    top: 13px;
    right: 62px;
}
.small-2{
    position: absolute;
    top: 280px;
    right: 60px;

}

.features-section{
    padding: 50px 0px;
    background-color: #13499f;
}
.fh{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 250;
    font-size: 42px;
    color: white;
    text-align: center;
    margin-bottom: 30px;


}
.feature-card{
    padding: 30px;
    background-color: white;
    align-items: center;
    border-radius: 10px; 
    

    
}
.feature-card p{
    text-align: justify;
    font-size: 18;
    line-height: 1.6rem; 
}
.feature-card h3{
    text-align: justify;
    padding-top: 35px;
    font-size: 20px ;
    font-weight: 100px;
}

.notification-section{
    background-color: #bbebff;
    padding: 50px 0px;
}


.notify-text{
    margin-top: 140px;
}
.notify-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 40px;
font-weight: 40px;
}

.notify-text p{
    text-align: justify;
    font-size: 18px;  

}
.list{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }


.seamless-section{
    background-color: white;
    padding: 70px 0px;
}
.seamless-text{
    margin-top: 140px;
}
.seamless-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 40px;
font-weight: 40px;
}
.seamless-text p{
    text-align: justify;
    font-size: 18px;  

}
.list-1{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }

/* Flexibility Section Styling */
.flexibility-section {
    background-color: #13499f;

}

.flexibility-content {
    padding: 40px 20px;
}

/* Custom Accordion Styling */
.flex-accordion-item {
    background-color: white;
    border: none;
    border-radius: 8px !important;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.flex-accordion-btn {
    background-color: white;
    border: none;
    color: #13499f;
    font-weight: 600;
    font-size: 18px;
    padding: 20px 25px;
    box-shadow: none;
    border-radius: 8px !important;
    display: flex;
    align-items: center;
}

.flex-accordion-btn:not(.collapsed) {
    background-color: white;
    color: #13499f;
    box-shadow: none;
}

.flex-accordion-btn:focus {
    box-shadow: none;
    border: none;
}

.flex-accordion-btn::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2313499f'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    margin-left: auto;
}

.flex-accordion-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.flex-accordion-body {
    background-color: white;
    padding: 25px;
    border-top: 1px solid #e9ecef;
}

.flex-accordion-body p {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
}

.flex-feature-icon {
    max-width: 60px;
    height: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .flexibility-section {
        padding: 50px 0;
    }

    .flexibility-content {
        padding: 20px 10px;
        margin-top: 30px;
    }

    .flex-accordion-btn {
        font-size: 16px;
        padding: 15px 20px;
    }

    .flex-accordion-body {
        padding: 20px;
    }
}